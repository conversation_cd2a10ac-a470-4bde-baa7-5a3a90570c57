{"name": "vite-plugin-tempest", "type": "module", "version": "1.6.0", "author": "<PERSON><PERSON>", "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["*.d.ts", "dist"], "scripts": {"build": "unbuild", "build:stub": "unbuild --stub", "prepublishOnly": "bun run test && bun run build", "test": "vitest run", "test:watch": "vitest"}, "peerDependencies": {"typescript": "^5.0.0", "vite": "^7.0.0"}, "dependencies": {"@innocenzi/utils": "^0.3.0", "picocolors": "^1.1.1"}}